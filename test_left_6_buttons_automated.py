#!/usr/bin/env python3
"""
Automated test program to test the Left 6 buttons (3 rotation + 3 movement) 
and verify that yellow text display and origin markers update correctly.
This runs completely automatically without user interaction.
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QFont

# Import the main program
from step_viewer_tdk_modular import StepViewerTDK

class AutomatedButtonTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Automated Left 6 Button Tester")
        self.setGeometry(100, 100, 1400, 900)
        
        # Test tracking
        self.test_step = 0
        self.test_results = []
        self.initial_values = {}
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)
        
        # Left panel - test results display
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_panel.setFixedWidth(400)
        
        # Status display
        self.status_label = QLabel("Status: Initializing...")
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        left_layout.addWidget(self.status_label)
        
        # Results display
        results_label = QLabel("Test Results:")
        results_label.setFont(QFont("Arial", 10, QFont.Bold))
        left_layout.addWidget(results_label)
        
        self.results_text = QTextEdit()
        self.results_text.setFont(QFont("Courier", 9))
        self.results_text.setMaximumHeight(600)
        left_layout.addWidget(self.results_text)
        
        # Manual controls (for debugging if needed)
        manual_label = QLabel("Manual Controls (for debugging):")
        manual_label.setFont(QFont("Arial", 10, QFont.Bold))
        left_layout.addWidget(manual_label)
        
        manual_btn = QPushButton("Start Automated Test")
        manual_btn.clicked.connect(self.start_automated_test)
        left_layout.addWidget(manual_btn)
        
        reset_btn = QPushButton("Reset to Original")
        reset_btn.clicked.connect(self.reset_viewer)
        left_layout.addWidget(reset_btn)
        
        left_layout.addStretch()
        layout.addWidget(left_panel)
        
        # Right side - The actual viewer
        self.viewer = StepViewerTDK()
        layout.addWidget(self.viewer)
        
        # Set active viewer to top
        self.viewer.active_viewer = "top"
        
        # Start the automated test sequence after a short delay
        QTimer.singleShot(2000, self.initialize_test)
        
    def log_result(self, message):
        """Add a result to the display"""
        print(message)  # Also print to console
        self.results_text.append(message)
        QApplication.processEvents()
        
    def initialize_test(self):
        """Initialize the test by loading a STEP file"""
        self.status_label.setText("Status: Loading STEP file...")
        self.log_result("🔥🔥🔥 AUTOMATED LEFT 6 BUTTON TEST STARTING 🔥🔥🔥")
        self.log_result("")
        
        # Load a STEP file
        step_file = 'SOIC16P127_1270X940X610L89X51.STEP'
        if os.path.exists(step_file):
            success = self.viewer.load_step_file_direct(step_file)
            if success:
                self.log_result(f"✅ Loaded STEP file: {step_file}")
                QTimer.singleShot(1000, self.capture_initial_values)
            else:
                self.log_result(f"❌ Failed to load STEP file: {step_file}")
                self.status_label.setText("Status: STEP file load failed")
        else:
            self.log_result(f"❌ STEP file not found: {step_file}")
            self.status_label.setText("Status: STEP file not found")
            
    def capture_initial_values(self):
        """Capture initial values before testing"""
        self.status_label.setText("Status: Capturing initial values...")
        self.log_result("📊 CAPTURING INITIAL VALUES:")
        
        # Capture rotation values
        if hasattr(self.viewer, 'current_rot_left'):
            self.initial_values['rotation'] = self.viewer.current_rot_left.copy()
            self.log_result(f"   Initial Rotation: X={self.initial_values['rotation']['x']:.1f}° Y={self.initial_values['rotation']['y']:.1f}° Z={self.initial_values['rotation']['z']:.1f}°")
        else:
            self.log_result("   ❌ No current_rot_left found")
            
        # Capture position values  
        if hasattr(self.viewer, 'current_pos_left'):
            self.initial_values['position'] = self.viewer.current_pos_left.copy()
            self.log_result(f"   Initial Position: X={self.initial_values['position']['x']:.3f}mm Y={self.initial_values['position']['y']:.3f}mm Z={self.initial_values['position']['z']:.3f}mm")
        else:
            self.log_result("   ❌ No current_pos_left found")
            
        # Check if yellow text overlay exists
        yellow_text_exists = False
        if hasattr(self.viewer, 'vtk_renderer_left') and hasattr(self.viewer.vtk_renderer_left, 'text_actor_left'):
            yellow_text_exists = self.viewer.vtk_renderer_left.text_actor_left is not None
        self.log_result(f"   Yellow Text Overlay: {'✅ EXISTS' if yellow_text_exists else '❌ MISSING'}")
        
        # Check if origin markers exist
        origin_markers_exist = False
        if hasattr(self.viewer, 'vtk_renderer_left'):
            if hasattr(self.viewer.vtk_renderer_left, 'origin_actors') and self.viewer.vtk_renderer_left.origin_actors:
                origin_markers_exist = True
        self.log_result(f"   Origin Markers: {'✅ EXISTS' if origin_markers_exist else '❌ MISSING'}")
        
        self.log_result("")
        QTimer.singleShot(1000, self.start_automated_test)
        
    def start_automated_test(self):
        """Start the automated button testing sequence"""
        self.status_label.setText("Status: Starting automated tests...")
        self.log_result("🚀 STARTING AUTOMATED BUTTON TESTS:")
        self.log_result("   Testing sequence: X+, X-, Y+, Y-, Z+, Z- (rotation), then X+, X-, Y+, Y-, Z+, Z- (movement)")
        self.log_result("")
        
        self.test_step = 0
        QTimer.singleShot(500, self.run_next_test)
        
    def run_next_test(self):
        """Run the next test in the sequence"""
        # Test sequence: 6 rotation tests + 6 movement tests = 12 total
        test_sequence = [
            # Rotation tests
            ('rotate', 'x', 15.0, "X+ Rotation"),
            ('rotate', 'x', -30.0, "X- Rotation (should go negative)"),
            ('rotate', 'x', 15.0, "X+ Rotation (back to zero)"),
            ('rotate', 'y', 15.0, "Y+ Rotation"),
            ('rotate', 'y', -15.0, "Y- Rotation (back to zero)"),
            ('rotate', 'z', 15.0, "Z+ Rotation"),
            ('rotate', 'z', -15.0, "Z- Rotation (back to zero)"),
            # Movement tests  
            ('move', 'x', 1.0, "X+ Movement"),
            ('move', 'x', -2.0, "X- Movement (should go negative)"),
            ('move', 'x', 1.0, "X+ Movement (back to zero)"),
            ('move', 'y', 1.0, "Y+ Movement"),
            ('move', 'y', -1.0, "Y- Movement (back to zero)"),
            ('move', 'z', 1.0, "Z+ Movement"),
            ('move', 'z', -1.0, "Z- Movement (back to zero)")
        ]
        
        if self.test_step >= len(test_sequence):
            # All tests complete
            self.complete_testing()
            return
            
        # Get current test
        test_type, axis, amount, description = test_sequence[self.test_step]
        
        self.log_result(f"🔧 TEST {self.test_step + 1}/14: {description}")
        self.status_label.setText(f"Status: Running test {self.test_step + 1}/14 - {description}")
        
        # Capture values before the test
        before_rot = self.viewer.current_rot_left.copy() if hasattr(self.viewer, 'current_rot_left') else {}
        before_pos = self.viewer.current_pos_left.copy() if hasattr(self.viewer, 'current_pos_left') else {}
        
        # Execute the button action
        if test_type == 'rotate':
            self.viewer.rotate_shape(axis, amount)
        else:  # move
            self.viewer.move_shape(axis, amount)
            
        # Allow time for updates
        QApplication.processEvents()
        
        # Schedule the result check
        QTimer.singleShot(200, lambda: self.check_test_result(test_type, axis, amount, description, before_rot, before_pos))
        
    def check_test_result(self, test_type, axis, amount, description, before_rot, before_pos):
        """Check the result of a test"""
        # Capture values after the test
        after_rot = self.viewer.current_rot_left.copy() if hasattr(self.viewer, 'current_rot_left') else {}
        after_pos = self.viewer.current_pos_left.copy() if hasattr(self.viewer, 'current_pos_left') else {}
        
        success = True
        issues = []
        
        if test_type == 'rotate':
            # Check rotation values
            expected_change = amount
            actual_change = after_rot.get(axis, 0) - before_rot.get(axis, 0)
            
            if abs(actual_change - expected_change) > 0.1:
                success = False
                issues.append(f"Rotation {axis.upper()} changed by {actual_change:.1f}° instead of {expected_change:.1f}°")
            else:
                self.log_result(f"   ✅ Rotation {axis.upper()}: {before_rot.get(axis, 0):.1f}° → {after_rot.get(axis, 0):.1f}° (change: {actual_change:.1f}°)")
                
        else:  # move
            # Check position values
            expected_change = amount
            actual_change = after_pos.get(axis, 0) - before_pos.get(axis, 0)
            
            if abs(actual_change - expected_change) > 0.01:
                success = False
                issues.append(f"Position {axis.upper()} changed by {actual_change:.3f}mm instead of {expected_change:.3f}mm")
            else:
                self.log_result(f"   ✅ Position {axis.upper()}: {before_pos.get(axis, 0):.3f}mm → {after_pos.get(axis, 0):.3f}mm (change: {actual_change:.3f}mm)")
        
        # Check if yellow text was updated (this is harder to verify programmatically)
        # For now, we'll assume it's working if the values changed
        
        # Report any issues
        if not success:
            for issue in issues:
                self.log_result(f"   ❌ {issue}")
        
        # Store result
        self.test_results.append({
            'test': description,
            'success': success,
            'issues': issues
        })
        
        self.log_result("")
        
        # Move to next test
        self.test_step += 1
        QTimer.singleShot(500, self.run_next_test)
        
    def complete_testing(self):
        """Complete the testing and show final results"""
        self.status_label.setText("Status: Testing complete!")
        
        self.log_result("🏁 TESTING COMPLETE!")
        self.log_result("="*50)
        
        # Count successes and failures
        successes = sum(1 for result in self.test_results if result['success'])
        failures = len(self.test_results) - successes
        
        self.log_result(f"📊 FINAL RESULTS:")
        self.log_result(f"   ✅ Successful tests: {successes}")
        self.log_result(f"   ❌ Failed tests: {failures}")
        self.log_result(f"   📈 Success rate: {(successes/len(self.test_results)*100):.1f}%")
        
        if failures > 0:
            self.log_result("")
            self.log_result("❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    self.log_result(f"   • {result['test']}")
                    for issue in result['issues']:
                        self.log_result(f"     - {issue}")
        
        self.log_result("")
        if failures == 0:
            self.log_result("🎉 ALL TESTS PASSED! The Left 6 buttons are working correctly.")
        else:
            self.log_result("🔧 SOME TESTS FAILED. The Left 6 buttons need attention.")
            
        self.log_result("")
        self.log_result("💡 NOTE: This test only checks if the internal values change correctly.")
        self.log_result("   Visual verification of yellow text and origin markers should be done manually.")
        
    def reset_viewer(self):
        """Reset the viewer to original state"""
        self.status_label.setText("Status: Resetting...")
        self.viewer.reset_to_original()
        self.log_result("🔄 Viewer reset to original state")

def main():
    print("🔥🔥🔥 STARTING AUTOMATED LEFT 6 BUTTON TEST 🔥🔥🔥")
    
    app = QApplication(sys.argv)
    
    # Create the test window
    tester = AutomatedButtonTester()
    tester.show()
    
    print("✅ Automated test window created")
    print("📋 This test will run automatically and show results")
    
    # Start the event loop
    app.exec_()

if __name__ == "__main__":
    main()
